print("创建修复脚本...")

fix_script = """
# 检查MoviePy版本
import pkg_resources
moviepy_version = pkg_resources.get_distribution("moviepy").version
print(f"当前MoviePy版本: {moviepy_version}")

# 修复video.py中的导入问题
import os

video_path = '/MoneyPrinterAICreate/app/services/video.py'
if os.path.exists(video_path):
    with open(video_path, 'r') as f:
        content = f.read()
    
    # 检查是否使用了moviepy.editor
    if 'from moviepy.editor import' in content:
        print("发现moviepy.editor导入，正在修复...")
        
        # 替换导入部分
        content = content.replace(
            'from moviepy.editor import (',
            'from moviepy import ('
        )
        
        # 保存修改后的文件
        with open(video_path, 'w') as f:
            f.write(content)
        print(f"已修复 {video_path}")
    else:
        print("未发现moviepy.editor导入，无需修复")
else:
    print(f"找不到文件: {video_path}")

print("修复完成")
"""

with open('fix_docker_moviepy.py', 'w') as f:
    f.write(fix_script)

print("修复脚本已创建，现在将其复制到Docker容器并执行...")

# 获取容器ID
container_id = input("请输入Docker容器ID（可以通过'docker ps'查看）: ")

# 复制脚本到容器
import os
os.system(f"docker cp fix_docker_moviepy.py {container_id}:/tmp/")

# 在容器中执行脚本
os.system(f"docker exec {container_id} python /tmp/fix_docker_moviepy.py")

print("修复完成，请重新启动WebUI")
