#!/usr/bin/env python3
# 测试Wan2.1 API连接

import os
import sys
import requests
import json
import time
import asyncio
from loguru import logger

# 设置日志
logger.remove()
logger.add(sys.stderr, level="INFO")

# API URL
api_url = "http://192.168.2.199:22333"

def test_connection():
    """测试API连接"""
    logger.info(f"测试连接到API: {api_url}")
    
    try:
        # 尝试直接访问根路径
        response = requests.get(api_url, timeout=5)
        logger.info(f"API响应状态码: {response.status_code}")
        logger.info(f"API响应内容: {response.text[:200]}...")
        return True
    except requests.exceptions.ConnectionError as e:
        logger.error(f"连接错误: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"其他错误: {str(e)}")
        return False

def test_endpoints():
    """测试各个API端点"""
    endpoints = [
        "/ping",
        "/health",
        "/generate",
        "/status/test",
        "/result/test"
    ]
    
    for endpoint in endpoints:
        try:
            logger.info(f"测试端点: {endpoint}")
            response = requests.get(f"{api_url}{endpoint}", timeout=5)
            logger.info(f"状态码: {response.status_code}")
            logger.info(f"响应内容: {response.text[:100]}...")
        except Exception as e:
            logger.error(f"请求失败: {str(e)}")

async def test_generate():
    """测试生成视频"""
    logger.info("测试视频生成...")
    
    # 准备请求数据
    data = {
        "prompt": "一只可爱的小猫在阳光下玩耍",
        "task": "t2v-1.3B",
        "size": "832*480",
        "sample_shift": 8,
        "sample_guide_scale": 6
    }
    
    try:
        # 发送请求
        logger.info(f"发送请求: {json.dumps(data)[:200]}...")
        response = requests.post(f"{api_url}/generate", json=data, timeout=30)
        logger.info(f"API响应状态码: {response.status_code}")
        
        if response.status_code != 202:
            logger.error(f"API请求错误: {response.status_code}, {response.text}")
            return False
        
        # 获取任务ID
        job_id = response.json().get("job_id")
        if not job_id:
            logger.error(f"未能获取任务ID，响应: {response.text}")
            return False
            
        logger.info(f"任务ID: {job_id}")
        
        # 轮询任务状态
        start_time = time.time()
        max_wait_time = 60  # 1分钟超时
        
        while time.time() - start_time < max_wait_time:
            try:
                status_response = requests.get(f"{api_url}/status/{job_id}", timeout=10)
                status_data = status_response.json()
                
                if status_data.get("status") == "completed":
                    logger.info(f"视频生成完成!")
                    break
                elif status_data.get("status") == "failed":
                    logger.error(f"视频生成失败: {status_data.get('error')}")
                    return False
                else:
                    elapsed = status_data.get("elapsed", 0)
                    logger.info(f"状态: {status_data.get('status')}, 已耗时: {elapsed:.2f}s")
                    await asyncio.sleep(5)
            except Exception as e:
                logger.error(f"获取状态失败: {str(e)}")
                await asyncio.sleep(5)
        else:
            logger.error(f"等待视频生成超时")
            return False
        
        # 测试下载视频
        try:
            download_response = requests.get(f"{api_url}/result/{job_id}", stream=True, timeout=10)
            
            if download_response.status_code != 200:
                logger.error(f"下载视频错误: {download_response.status_code}, {download_response.text}")
                return False
            
            logger.info("视频下载成功")
            return True
        except Exception as e:
            logger.error(f"下载视频失败: {str(e)}")
            return False
    
    except Exception as e:
        logger.error(f"生成测试失败: {str(e)}")
        return False

async def main():
    """主函数"""
    # 测试连接
    if not test_connection():
        logger.error("API连接测试失败，退出测试")
        return
    
    # 测试端点
    test_endpoints()
    
    # 测试生成视频
    generate_success = await test_generate()
    logger.info(f"视频生成测试: {'成功' if generate_success else '失败'}")

if __name__ == "__main__":
    asyncio.run(main()) 