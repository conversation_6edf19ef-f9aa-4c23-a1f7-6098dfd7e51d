with open('app/services/video.py', 'r') as f:
    content = f.read()

# 修复导入部分
import_section = """
import glob
import itertools
import os
import random
import gc
import shutil
from typing import List
from loguru import logger
from moviepy import (
    AudioFileClip,
    ColorClip,
    CompositeAudioClip,
    CompositeVideoClip,
    ImageClip,
    TextClip,
    VideoFileClip,
    afx,
    concatenate_videoclips,
)
from moviepy.video.tools.subtitles import SubtitlesClip
from PIL import ImageFont

from app.models import const
from app.models.schema import (
    MaterialInfo,
    VideoAspect,
    VideoConcatMode,
    VideoParams,
    VideoTransitionMode,
)
"""

# 替换导入部分
import_start = content.find("import ")
if import_start >= 0:
    # 找到导入部分的结束位置
    import_end = content.find("from app.models")
    if import_end >= 0:
        next_section_start = content.find("\n\n", import_end)
        if next_section_start >= 0:
            # 替换整个导入部分
            content = content[:import_start] + import_section + content[next_section_start:]

# 保存修改后的文件
with open('app/services/video.py', 'w') as f:
    f.write(content)

print("已修复MoviePy导入问题")
