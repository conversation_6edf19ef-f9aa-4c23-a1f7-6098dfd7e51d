# Docker环境MoviePy修复指南

## 问题描述
在Docker环境中运行时，可能会遇到以下错误：
```
ModuleNotFoundError: No module named 'moviepy.editor'
```

## 解决方案

### 方法1：使用修复脚本（推荐）

1. 首先找到正在运行的Docker容器ID：
```bash
docker ps
```

2. 将修复脚本复制到容器中：
```bash
docker cp fix_moviepy_docker.py <容器ID>:/tmp/
```

3. 在容器中执行修复脚本：
```bash
docker exec <容器ID> python /tmp/fix_moviepy_docker.py
```

4. 重启容器：
```bash
docker restart <容器ID>
```

### 方法2：手动修复

1. 进入Docker容器：
```bash
docker exec -it <容器ID> /bin/bash
```

2. 在容器中安装MoviePy：
```bash
pip install moviepy==1.0.3
```

3. 测试安装：
```bash
python -c "from moviepy.editor import VideoFileClip; print('MoviePy安装成功')"
```

4. 退出容器并重启：
```bash
exit
docker restart <容器ID>
```

### 方法3：重新构建Docker镜像

如果上述方法都不行，可以重新构建Docker镜像：

1. 停止当前容器：
```bash
docker stop <容器ID>
```

2. 重新构建镜像：
```bash
docker build -t moneyprinterturbo .
```

3. 重新运行容器：
```bash
# Linux/MacOS
docker run -v $(pwd)/config.toml:/MoneyPrinterTurbo/config.toml -v $(pwd)/storage:/MoneyPrinterTurbo/storage -p 8501:8501 moneyprinterturbo

# Windows
docker run -v ${PWD}/config.toml:/MoneyPrinterTurbo/config.toml -v ${PWD}/storage:/MoneyPrinterTurbo/storage -p 8501:8501 moneyprinterturbo
```

## 验证修复

修复完成后，访问 http://localhost:8501 应该能正常看到界面，并且不会出现MoviePy相关的错误。

## 其他修复内容

本次修复还包括：

1. ✅ 修复了MoviePy 1.0.3版本兼容性问题
   - 将`with_duration`改为`set_duration`
   - 将`with_position`改为`set_position`
   - 将`with_audio`改为`set_audio`
   - 将`resized`改为`resize`
   - 修复了音频效果的导入和使用方式

2. ✅ 添加了缺失的常量定义
   - 添加了`TASK_STATE_RUNNING = 2`

3. ✅ 更新了项目标题
   - 从`MoneyPrinterTurbo v1.2.6`改为`MoneyPrinterTurbo-Wan2.1 v2.1.0`

## 注意事项

- 确保Docker容器有足够的内存来安装和运行MoviePy
- 如果仍然有问题，可以查看容器日志：`docker logs <容器ID>`
- 建议使用最新的Docker版本
