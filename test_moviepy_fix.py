#!/usr/bin/env python3
"""
测试MoviePy修复是否成功
"""

def test_moviepy_imports():
    """测试MoviePy导入"""
    try:
        from moviepy.editor import (
            AudioFileClip,
            ColorClip,
            CompositeAudioClip,
            CompositeVideoClip,
            ImageClip,
            TextClip,
            VideoFileClip,
            afx,
            concatenate_videoclips,
        )
        print("✓ MoviePy导入成功")
        return True
    except Exception as e:
        print(f"✗ MoviePy导入失败: {e}")
        return False

def test_colorclip_methods():
    """测试ColorClip方法"""
    try:
        from moviepy.editor import ColorClip
        
        # 测试使用duration参数创建ColorClip
        clip = ColorClip(size=(100, 100), color=(0, 0, 0), duration=1.0)
        print("✓ ColorClip创建成功")
        
        # 测试set_position方法
        clip = clip.set_position("center")
        print("✓ ColorClip.set_position方法可用")
        
        clip.close()
        return True
    except Exception as e:
        print(f"✗ ColorClip测试失败: {e}")
        return False

def test_video_methods():
    """测试VideoClip方法"""
    try:
        from moviepy.editor import VideoFileClip
        
        # 检查方法是否存在
        methods = dir(VideoFileClip)
        
        required_methods = ['resize', 'set_audio', 'set_position', 'set_duration', 'fx']
        missing_methods = [m for m in required_methods if m not in methods]
        
        if missing_methods:
            print(f"✗ VideoFileClip缺少方法: {missing_methods}")
            return False
        
        print("✓ VideoFileClip所有必需方法都存在")
        return True
    except Exception as e:
        print(f"✗ VideoFileClip测试失败: {e}")
        return False

def test_audio_methods():
    """测试AudioClip方法"""
    try:
        from moviepy.editor import AudioFileClip, afx
        
        # 检查方法是否存在
        methods = dir(AudioFileClip)
        
        required_methods = ['fx', 'set_duration']
        missing_methods = [m for m in required_methods if m not in methods]
        
        if missing_methods:
            print(f"✗ AudioFileClip缺少方法: {missing_methods}")
            return False
        
        print("✓ AudioFileClip所有必需方法都存在")
        
        # 检查afx模块
        afx_methods = dir(afx)
        required_afx = ['MultiplyVolume', 'AudioFadeOut', 'AudioLoop']
        missing_afx = [m for m in required_afx if m not in afx_methods]
        
        if missing_afx:
            print(f"✗ afx模块缺少方法: {missing_afx}")
            return False
        
        print("✓ afx模块所有必需方法都存在")
        return True
    except Exception as e:
        print(f"✗ AudioFileClip测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试MoviePy修复...")
    print("=" * 50)
    
    tests = [
        test_moviepy_imports,
        test_colorclip_methods,
        test_video_methods,
        test_audio_methods,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！MoviePy修复成功！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    main()
