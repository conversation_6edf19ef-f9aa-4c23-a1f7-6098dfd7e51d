{"Language": "English", "Translation": {"Login Required": "<PERSON><PERSON> Required", "Please login to access settings": "Please login to access settings (:gray[default username: admin, password: admin, which you can change in config.toml])", "Username": "Username", "Password": "Password", "Login": "<PERSON><PERSON>", "Login Error": "<PERSON><PERSON>", "Incorrect username or password": "Incorrect username or password", "Please enter your username and password": "Please enter your username and password", "Video Script Settings": "**<PERSON><PERSON>t Settings**", "Video Subject": "Video Topic (provide a keyword, :red[AI will auto-generate] video script)", "Script Language": "Script Language (Usually AI will auto-detect based on your input language)", "Generate Video Script and Keywords": "Generate Video Script and Keywords from **Topic** with AI", "Auto Detect": "Auto Detect", "Video Script": "Video Script (:blue[① Optional, can use AI to generate ② Use punctuation properly for better subtitle generation])", "Generate Video Keywords": "Generate Video Keywords from **Script** with AI", "Please Enter the Video Subject": "Please Enter the Video Subject", "Generating Video Script and Keywords": "AI is generating video script and keywords...", "Generating Video Keywords": "AI is generating video keywords...", "Video Keywords": "Video Keywords (:blue[① Optional, can use AI to generate ② Separate with **commas**, English only])", "Video Settings": "**Video Settings**", "Video Generation Mode": "Video Generation Mode", "Pexels/Pixabay": "Pexels/Pixabay (Web materials)", "Wan2.1 Official API": "Wan2.1 Official API (AI generation)", "Wan2.1 Custom API": "Wan2.1 Custom API (Self-hosted)", "Wan2.1 API Key": "Wan2.1 API Key", "Wan2.1 Custom API URL": "Wan2.1 Custom API URL", "Please enter your Wan2.1 API key. You need to register at https://wan-ai-wan-2-1.ms.show/ to get it.": "Please enter your Wan2.1 API key. You need to register at https://wan-ai-wan-2-1.ms.show/ to get it.", "Please enter your Wan2.1 custom API URL. This should be the base URL of your self-hosted Wan2.1 API server.": "Please enter your Wan2.1 custom API URL. This should be the base URL of your self-hosted Wan2.1 API server.", "Use Storyboard": "Use Storyboard", "Storyboard Settings": "Storyboard Settings", "Segment": "Segment", "Text": "Text", "Prompt": "Prompt", "Image": "Image", "Add Segment": "Add Segment", "Generate Segments from Script": "Generate Segments from Script", "Clear Segments": "Clear Segments", "Generated": "Generated", "segments": "segments", "Please enter a video script first": "Please enter a video script first", "Video Concat Mode": "Video Concat Mode", "Random": "Random (Recommended)", "Sequential": "Sequential", "Video Transition Mode": "Video Transition Mode", "None": "None", "Shuffle": "Shuffle", "FadeIn": "FadeIn", "FadeOut": "FadeOut", "SlideIn": "SlideIn", "SlideOut": "SlideOut", "Video Ratio": "Video Ratio", "Portrait": "Portrait 9:16 (<PERSON><PERSON><PERSON><PERSON>)", "Landscape": "Landscape 16:9 (YouTube)", "Clip Duration": "Clip Duration (seconds) (**Not** the total video length, but the length of each **clip**)", "Number of Videos Generated Simultaneously": "Number of Videos Generated Simultaneously", "Audio Settings": "**Audio Settings**", "Speech Synthesis": "Speech Synthesis (:red[**Match the script language**. Note: V2 version has better quality but needs API KEY])", "Speech Region": "Service Region (:red[Required, [Click to get](https://portal.azure.com/#view/Microsoft_Azure_ProjectOxford/CognitiveServicesHub/~/SpeechServices)])", "Speech Key": "API Key (:red[Required, either Key 1 or Key 2 [Click to get](https://portal.azure.com/#view/Microsoft_Azure_ProjectOxford/CognitiveServicesHub/~/SpeechServices)])", "Speech Volume": "Speech Volume (1.0 means 100%)", "Speech Rate": "Speech Rate (1.0 means normal speed)", "Male": "Male", "Female": "Female", "Background Music": "Background Music", "No Background Music": "No Background Music", "Random Background Music": "Random Background Music", "Custom Background Music": "Custom Background Music", "Custom Background Music File": "Custom Background Music File Path", "Background Music Volume": "Background Music Volume (0.2 means 20%, keep it low)", "Subtitle Settings": "**Subtitle Settings**", "Enable Subtitles": "Enable Subtitles (If unchecked, settings below won't take effect)", "Font": "Font", "Position": "Position", "Top": "Top", "Center": "Center", "Bottom": "Bottom (Recommended)", "Custom": "Custom Position (70 means 70% from the top)", "Font Size": "Font Size", "Font Color": "Font Color", "Stroke Color": "Stroke Color", "Stroke Width": "Stroke Width", "Generate Video": "Generate Video", "Video Script and Subject Cannot Both Be Empty": "Video Script and Subject Cannot Both Be Empty", "Generating Video": "Generating Video, please wait...", "Start Generating Video": "Start Generating Video", "Video Generation Completed": "Video Generation Completed", "Video Generation Failed": "Video Generation Failed", "You can download the generated video from the following links": "You can download the generated video from the following links", "Basic Settings": "**Basic Settings** (:blue[Click to expand])", "Language": "Interface Language", "Pexels API Key": "Pexels API Key ([Click to get](https://www.pexels.com/api/)) :red[Recommended]", "Pixabay API Key": "Pixabay API Key ([Click to get](https://pixabay.com/api/docs/#api_search_videos)) :red[Optional, use if P<PERSON><PERSON> is unavailable]", "LLM Provider": "LLM Provider", "API Key": "API Key (:red[Required, get from LLM provider])", "Base Url": "Base URL (Optional)", "Account ID": "Account ID (For Cloudflare, get from dash panel URL)", "Model Name": "Model Name (:blue[Check the authorized models in LLM provider backend])", "Please Enter the LLM API Key": "Please Enter the LLM API Key", "Please Enter the Pexels API Key": "Please Enter the Pexels API Key", "Please Enter the Pixabay API Key": "Please Enter the Pixabay API Key", "Get Help": "For help or suggestions, join our community: https://harryai.cc", "Video Source": "Video Source", "TikTok": "<PERSON><PERSON><PERSON><PERSON> (Coming Soon)", "Bilibili": "<PERSON><PERSON><PERSON><PERSON> (Coming Soon)", "Xiaohongshu": "<PERSON><PERSON><PERSON> (Coming Soon)", "Local file": "Local file", "Play Voice": "Test Voice Synthesis", "Voice Example": "This is a test of voice synthesis.", "Synthesizing Voice": "Synthesizing voice, please wait...", "TTS Provider": "TTS Provider", "TTS Servers": "TTS Servers", "No voices available for the selected TTS server. Please select another server.": "No voices available for the selected TTS server. Please select another server.", "SiliconFlow API Key": "SiliconFlow API Key [Click to get](https://cloud.siliconflow.cn/account/ak)", "SiliconFlow TTS Settings": "SiliconFlow TTS Settings", "Speed: Range [0.25, 4.0], default is 1.0": "Speed: Range [0.25, 4.0], default is 1.0", "Volume: Uses Speech Volume setting, default 1.0 maps to gain 0": "Volume: Uses Speech Volume setting, default 1.0 maps to gain 0", "Hide Log": "<PERSON>de Log", "Hide Basic Settings": "Hide Basic Settings\n\nWhen hidden, the basic settings panel will not be displayed on the page.\n\nTo show it again, set `hide_config = false` in `config.toml`", "LLM Settings": "**<PERSON><PERSON>**", "Video Source Settings": "**Video Source Settings**", "Video Resolution": "Video Resolution", "Normal (1080p)": "Normal (1080p)", "High (2K)": "High (2K)", "Ultra (4K)": "Ultra (4K)"}}