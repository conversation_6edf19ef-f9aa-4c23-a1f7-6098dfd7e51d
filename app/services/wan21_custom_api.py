import os
import requests
import json
import time
import base64
import shutil
import asyncio
from uuid import uuid4
from loguru import logger

from app.config import config
from app.utils import utils

# 确保临时目录存在
tmp_path = "tmp"
os.makedirs(tmp_path, exist_ok=True)

async def t2v(prompt, seg_id):
    """
    使用自定义API进行文本到视频生成
    
    Args:
        prompt (str): 提示词
        seg_id (int): 段落ID
        
    Returns:
        str: 生成的视频路径
    """
    try:
        logger.info(f"[seg{seg_id+1}]-t2v-开始处理")
        
        # 从配置获取API URL
        api_url = config.app.get("wan21_custom_api_url", "http://localhost:22333")
        logger.info(f"[seg{seg_id+1}]-t2v-使用API: {api_url}")
        
        # 准备请求数据 - 使用MoneyPrinterAICreate中的参数
        data = {
            "prompt": prompt,
            "task": "t2v-1.3B",
            "size": "832*480",
            "sample_shift": 8,
            "sample_guide_scale": 6
        }
        
        # 发送请求开始生成
        try:
            logger.info(f"[seg{seg_id+1}]-t2v-发送请求: {json.dumps(data)[:200]}...")
            response = requests.post(f"{api_url}/generate", json=data, timeout=30)
            logger.info(f"[seg{seg_id+1}]-t2v-API响应状态码: {response.status_code}")
            
            if response.status_code != 202:
                logger.error(f"[seg{seg_id+1}]-t2v-API请求错误: {response.status_code}, {response.text}")
                return None
        except requests.exceptions.RequestException as e:
            logger.error(f"[seg{seg_id+1}]-t2v-API连接错误: {str(e)}")
            return None
        
        # 获取任务ID
        job_id = response.json().get("job_id")
        if not job_id:
            logger.error(f"[seg{seg_id+1}]-t2v-未能获取任务ID，响应: {response.text}")
            return None
            
        logger.info(f"[seg{seg_id+1}]-t2v-任务ID: {job_id}")
        
        # 轮询任务状态
        start_time = time.time()
        max_wait_time = 600  # 10分钟超时
        
        while time.time() - start_time < max_wait_time:
            try:
                status_response = requests.get(f"{api_url}/status/{job_id}", timeout=10)
                status_data = status_response.json()
                
                if status_data.get("status") == "completed":
                    logger.info(f"[seg{seg_id+1}]-t2v-视频生成完成!")
                    break
                elif status_data.get("status") == "failed":
                    logger.error(f"[seg{seg_id+1}]-t2v-视频生成失败: {status_data.get('error')}")
                    return None
                else:
                    elapsed = status_data.get("elapsed", 0)
                    logger.info(f"[seg{seg_id+1}]-t2v-状态: {status_data.get('status')}, 已耗时: {elapsed:.2f}s")
                    await asyncio.sleep(5)
            except Exception as e:
                logger.error(f"[seg{seg_id+1}]-t2v-获取状态失败: {str(e)}")
                await asyncio.sleep(5)
        else:
            logger.error(f"[seg{seg_id+1}]-t2v-等待视频生成超时")
            return None
        
        # 生成唯一的输出文件名
        temp_dir = utils.storage_dir("temp", create=True)
        output_file = os.path.join(temp_dir, f"segment_{seg_id}_{str(uuid4())}.mp4")
        
        # 下载视频
        try:
            download_response = requests.get(f"{api_url}/result/{job_id}", stream=True, timeout=60)
            
            if download_response.status_code != 200:
                logger.error(f"[seg{seg_id+1}]-t2v-下载视频错误: {download_response.status_code}, {download_response.text}")
                return None
            
            # 保存视频到文件
            with open(output_file, 'wb') as f:
                for chunk in download_response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # 确认文件存在且大小大于0
            if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                logger.info(f"[seg{seg_id+1}]-t2v-[success]-output: {output_file}")
                return output_file
            else:
                logger.error(f"[seg{seg_id+1}]-t2v-下载的视频文件无效或为空")
                return None
        except Exception as e:
            logger.error(f"[seg{seg_id+1}]-t2v-下载视频失败: {str(e)}")
            return None
        
    except Exception as e:
        logger.error(f"[seg{seg_id+1}]-t2v-异常: {str(e)}")
        return None

def generate_video(prompt, output_path, api_url=None):
    """
    同步版本的视频生成函数，供Streamlit界面调用
    
    Args:
        prompt (str): 提示词
        output_path (str): 输出视频路径
        api_url (str, optional): API URL，如果不提供则从配置中读取
        
    Returns:
        bool: 是否成功生成视频
    """
    try:
        # 设置API URL
        if api_url:
            config.app["wan21_custom_api_url"] = api_url
        
        # 获取API URL
        api_url = config.app.get("wan21_custom_api_url", "http://localhost:22333")
        
        # 检查API URL是否可访问 - 尝试访问health端点
        try:
            # 尝试连接API服务器
            test_response = requests.get(f"{api_url}/health", timeout=5)
                
            if test_response.status_code != 200:
                logger.error(f"API服务器健康检查失败: {test_response.status_code}")
                return False, f"API服务器健康检查失败: {test_response.status_code}"
            logger.info(f"成功连接到API服务器: {api_url}")
        except requests.exceptions.ConnectionError:
            error_msg = f"无法连接到API服务器: {api_url}，请确保服务器已启动并且URL正确"
            logger.error(error_msg)
            return False, error_msg
        except requests.exceptions.Timeout:
            error_msg = f"连接API服务器超时: {api_url}"
            logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"连接API服务器时出错: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
        
        # 创建事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 调用异步函数
        result_path = loop.run_until_complete(t2v(prompt, 0))
        loop.close()
        
        if not result_path or not os.path.exists(result_path):
            error_msg = f"生成视频失败: {prompt}"
            logger.error(error_msg)
            return False, error_msg
        
        # 移动到指定路径
        shutil.copy(result_path, output_path)
        logger.info(f"视频已保存到: {output_path}")
        
        return True, "视频生成成功"
    except Exception as e:
        error_msg = f"生成视频失败: {str(e)}"
        logger.error(error_msg)
        return False, error_msg 