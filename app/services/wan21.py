import os
import requests
import json
import time
import asyncio
import shutil
from uuid import uuid4
from loguru import logger

from app.config import config
from app.utils import utils

async def generate_with_official_api(prompt, api_key, seg_id=0):
    """
    使用官方Wan2.1 API生成视频
    
    Args:
        prompt (str): 提示词
        api_key (str): API密钥
        seg_id (int): 段落ID
        
    Returns:
        str: 生成的视频路径
    """
    try:
        logger.info(f"[seg{seg_id+1}]-wan21-开始处理")
        
        # 检查API密钥
        if not api_key:
            logger.error(f"[seg{seg_id+1}]-wan21-未提供API密钥")
            return None
        
        # 准备请求头和数据
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        data = {
            "prompt": prompt,
            "negative_prompt": "low quality, bad quality, sketches",
            "model_name": "wan-2.1",
            "width": 1024,
            "height": 576,
            "num_frames": 16,
            "num_inference_steps": 25,
            "guidance_scale": 9.0
        }
        
        # 发送请求
        logger.info(f"[seg{seg_id+1}]-wan21-发送请求")
        response = requests.post(
            "https://api.wan-2-1.ms.show/v1/videos/generations",
            headers=headers,
            json=data
        )
        
        if response.status_code != 200:
            logger.error(f"[seg{seg_id+1}]-wan21-API请求失败: {response.status_code} {response.text}")
            return None
        
        # 解析响应
        result = response.json()
        video_url = result.get("video_url")
        
        if not video_url:
            logger.error(f"[seg{seg_id+1}]-wan21-未获取到视频URL")
            return None
        
        logger.info(f"[seg{seg_id+1}]-wan21-视频URL: {video_url}")
        
        # 下载视频
        video_response = requests.get(video_url, stream=True)
        
        if video_response.status_code != 200:
            logger.error(f"[seg{seg_id+1}]-wan21-下载视频失败: {video_response.status_code}")
            return None
        
        # 保存视频
        temp_dir = utils.storage_dir("temp", create=True)
        output_file = os.path.join(temp_dir, f"segment_{seg_id}_{str(uuid4())}.mp4")
        
        with open(output_file, "wb") as f:
            for chunk in video_response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        logger.info(f"[seg{seg_id+1}]-wan21-视频已保存: {output_file}")
        
        return output_file
    
    except Exception as e:
        logger.error(f"[seg{seg_id+1}]-wan21-处理失败: {str(e)}")
        return None

def generate_video(prompt, output_path, api_key=None):
    """
    同步版本的视频生成函数，供Streamlit界面调用
    
    Args:
        prompt (str): 提示词
        output_path (str): 输出视频路径
        api_key (str, optional): API密钥，如果不提供则从配置中读取
        
    Returns:
        bool: 是否成功生成视频
    """
    try:
        # 获取API密钥
        if not api_key:
            api_key = config.app.get("wan21_api_key", "")
        
        if not api_key:
            error_msg = "未提供Wan2.1 API密钥，请在设置中填写API密钥"
            logger.error(error_msg)
            return False, error_msg
        
        # 创建事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 调用异步函数
        result_path = loop.run_until_complete(generate_with_official_api(prompt, api_key))
        loop.close()
        
        if not result_path or not os.path.exists(result_path):
            error_msg = f"生成视频失败: {prompt}"
            logger.error(error_msg)
            return False, error_msg
        
        # 移动到指定路径
        shutil.copy(result_path, output_path)
        logger.info(f"视频已保存到: {output_path}")
        
        return True, "视频生成成功"
    except Exception as e:
        error_msg = f"生成视频失败: {str(e)}"
        logger.error(error_msg)
        return False, error_msg 