import os
import random
import re
import requests
import urllib3
from typing import List
import asyncio
from urllib.parse import urlencode

from loguru import logger
from moviepy.video.io.VideoFileClip import Video<PERSON>ileClip

from app.config import config
from app.models import const
from app.models.schema import MaterialInfo, VideoAspect, VideoConcatMode, VideoGenerationMode, StoryboardSegment
from app.utils import utils
from app.services import wan21, wan21_custom_api

# Disable insecure request warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

requested_count = 0


def get_api_key(cfg_key: str):
    api_keys = config.app.get(cfg_key)
    if not api_keys:
        raise ValueError(
            f"\n\n##### {cfg_key} is not set #####\n\nPlease set it in the config.toml file: {config.config_file}\n\n"
            f"{utils.to_json(config.app)}"
        )

    # if only one key is provided, return it
    if isinstance(api_keys, str):
        return api_keys

    global requested_count
    requested_count += 1
    return api_keys[requested_count % len(api_keys)]


def search_videos_pexels(
    search_term: str,
    minimum_duration: int,
    video_aspect: VideoAspect = VideoAspect.portrait,
) -> List[MaterialInfo]:
    aspect = VideoAspect(video_aspect)
    video_orientation = aspect.name
    
    # Get resolution level from config
    resolution_level = config.ui.get("video_resolution", "normal")
    logger.info(f"using video resolution level: {resolution_level}")
    
    video_width, video_height = aspect.to_resolution(resolution_level)
    api_key = get_api_key("pexels_api_keys")
    headers = {
        "Authorization": api_key,
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    }
    # Build URL
    params = {"query": search_term, "per_page": 20, "orientation": video_orientation}
    query_url = f"https://api.pexels.com/videos/search?{urlencode(params)}"
    logger.info(f"searching videos: {query_url}, with proxies: {config.proxy}")

    try:
        r = requests.get(
            query_url,
            headers=headers,
            proxies=config.proxy,
            verify=False,
            timeout=(30, 60),
        )
        response = r.json()
        video_items = []
        if "videos" not in response:
            logger.error(f"search videos failed: {response}")
            return video_items
        videos = response["videos"]
        # loop through each video in the result
        for v in videos:
            duration = v["duration"]
            # check if video has desired minimum duration
            if duration < minimum_duration:
                continue
            video_files = v["video_files"]
            
            # Sort video files by resolution (width * height) in descending order
            # This ensures we select the highest quality video available
            video_files = sorted(
                video_files, 
                key=lambda x: int(x.get("width", 0)) * int(x.get("height", 0)), 
                reverse=True
            )
            
            best_match = None
            # First try to find an exact match for our target resolution
            for video in video_files:
                w = int(video["width"])
                h = int(video["height"])
                if w == video_width and h == video_height:
                    best_match = video
                    break
            
            # If no exact match, use the highest resolution available
            if best_match is None and video_files:
                best_match = video_files[0]
                logger.info(f"No exact resolution match found, using highest available: {best_match['width']}x{best_match['height']}")
                
            if best_match:
                item = MaterialInfo()
                item.provider = "pexels"
                item.url = best_match["link"]
                item.duration = duration
                video_items.append(item)
            
        return video_items
    except Exception as e:
        logger.error(f"search videos failed: {str(e)}")

    return []


def search_videos_pixabay(
    search_term: str,
    minimum_duration: int,
    video_aspect: VideoAspect = VideoAspect.portrait,
) -> List[MaterialInfo]:
    aspect = VideoAspect(video_aspect)

    video_width, video_height = aspect.to_resolution()

    api_key = get_api_key("pixabay_api_keys")
    # Build URL
    params = {
        "q": search_term,
        "video_type": "all",  # Accepted values: "all", "film", "animation"
        "per_page": 50,
        "key": api_key,
    }
    query_url = f"https://pixabay.com/api/videos/?{urlencode(params)}"
    logger.info(f"searching videos: {query_url}, with proxies: {config.proxy}")

    try:
        r = requests.get(
            query_url, proxies=config.proxy, verify=False, timeout=(30, 60)
        )
        response = r.json()
        video_items = []
        if "hits" not in response:
            logger.error(f"search videos failed: {response}")
            return video_items
        videos = response["hits"]
        # loop through each video in the result
        for v in videos:
            duration = v["duration"]
            # check if video has desired minimum duration
            if duration < minimum_duration:
                continue
            video_files = v["videos"]
            # loop through each url to determine the best quality
            for video_type in video_files:
                video = video_files[video_type]
                w = int(video["width"])
                # h = int(video["height"])
                if w >= video_width:
                    item = MaterialInfo()
                    item.provider = "pixabay"
                    item.url = video["url"]
                    item.duration = duration
                    video_items.append(item)
                    break
        return video_items
    except Exception as e:
        logger.error(f"search videos failed: {str(e)}")

    return []


def save_video(video_url: str, save_dir: str = "") -> str:
    if not save_dir:
        save_dir = utils.storage_dir("cache_videos")

    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    url_without_query = video_url.split("?")[0]
    url_hash = utils.md5(url_without_query)
    video_id = f"vid-{url_hash}"
    video_path = f"{save_dir}/{video_id}.mp4"

    # if video already exists, return the path
    if os.path.exists(video_path) and os.path.getsize(video_path) > 0:
        logger.info(f"video already exists: {video_path}")
        return video_path

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }

    # if video does not exist, download it
    with open(video_path, "wb") as f:
        f.write(
            requests.get(
                video_url,
                headers=headers,
                proxies=config.proxy,
                verify=False,
                timeout=(60, 240),
            ).content
        )

    if os.path.exists(video_path) and os.path.getsize(video_path) > 0:
        try:
            clip = VideoFileClip(video_path)
            duration = clip.duration
            fps = clip.fps
            clip.close()
            if duration > 0 and fps > 0:
                return video_path
        except Exception as e:
            try:
                os.remove(video_path)
            except Exception:
                pass
            logger.warning(f"invalid video file: {video_path} => {str(e)}")
    return ""


# 添加Wan2.1视频生成函数
async def generate_wan21_videos(
    search_terms: List[str],
    video_aspect: VideoAspect = VideoAspect.portrait,
    segments: List[StoryboardSegment] = None,
) -> List[MaterialInfo]:
    """
    使用Wan2.1生成视频
    
    Args:
        search_terms: 搜索词列表，用于生成提示词
        video_aspect: 视频比例
        segments: 分镜段落列表，如果提供则按分镜生成
        
    Returns:
        List[MaterialInfo]: 生成的视频素材列表
    """
    logger.info(f"使用Wan2.1生成视频, 生成模式: {config.app.get('video_generation_mode', 'pixels')}")
    
    video_materials = []
    generation_mode = config.app.get("video_generation_mode", VideoGenerationMode.pixels.value)
    
    # 如果提供了分镜，则按分镜生成
    if segments:
        logger.info(f"使用分镜生成视频，共{len(segments)}个分镜")
        for i, segment in enumerate(segments):
            # 检查分镜是否已经有视频路径
            if segment.video_path and os.path.exists(segment.video_path):
                logger.info(f"分镜{i+1}已有视频: {segment.video_path}")
                video_path = segment.video_path
                
                # 获取视频时长
                try:
                    with VideoFileClip(video_path) as clip:
                        duration = int(clip.duration)
                except Exception as e:
                    logger.error(f"获取视频时长失败: {e}")
                    duration = 5  # 默认5秒
                
                material = MaterialInfo(
                    provider="local",
                    url=video_path,
                    duration=duration,
                    prompt=segment.prompt
                )
                video_materials.append(material)
                
                # 更新分镜信息
                segment.duration = duration
                continue
                
            prompt = segment.prompt
            if not prompt:
                prompt = f"{search_terms[i % len(search_terms)]} {segment.text}"
            
            video_path = ""
            if segment.image_path and os.path.exists(segment.image_path):
                # 使用图片生成视频
                logger.info(f"使用图片生成视频: {segment.image_path}")
                if generation_mode == VideoGenerationMode.wan21.value:
                    video_path = await wan21.i2v(prompt, segment.image_path, i)
                elif generation_mode == VideoGenerationMode.wan21_custom.value:
                    video_path = await wan21_custom_api.i2v(prompt, segment.image_path, i)
            else:
                # 使用文本生成视频
                logger.info(f"使用文本生成视频: {prompt}")
                if generation_mode == VideoGenerationMode.wan21.value:
                    video_path = await wan21.t2v(prompt, i)
                elif generation_mode == VideoGenerationMode.wan21_custom.value:
                    video_path = await wan21_custom_api.t2v(prompt, i)
            
            if video_path and os.path.exists(video_path):
                # 获取视频时长
                try:
                    with VideoFileClip(video_path) as clip:
                        duration = int(clip.duration)
                except Exception as e:
                    logger.error(f"获取视频时长失败: {e}")
                    duration = 5  # 默认5秒
                
                material = MaterialInfo(
                    provider="wan21",
                    url=video_path,
                    duration=duration,
                    prompt=prompt
                )
                video_materials.append(material)
                
                # 更新分镜信息
                segment.video_path = video_path
                segment.duration = duration
    else:
        # 不使用分镜，直接生成视频
        for i, term in enumerate(search_terms):
            prompt = term
            video_path = ""
            
            # 使用文本生成视频
            logger.info(f"使用文本生成视频: {prompt}")
            if generation_mode == VideoGenerationMode.wan21.value:
                video_path = await wan21.t2v(prompt, i)
            elif generation_mode == VideoGenerationMode.wan21_custom.value:
                video_path = await wan21_custom_api.t2v(prompt, i)
            
            if video_path and os.path.exists(video_path):
                # 获取视频时长
                try:
                    with VideoFileClip(video_path) as clip:
                        duration = int(clip.duration)
                except Exception as e:
                    logger.error(f"获取视频时长失败: {e}")
                    duration = 5  # 默认5秒
                
                material = MaterialInfo(
                    provider="wan21",
                    url=video_path,
                    duration=duration,
                    prompt=prompt
                )
                video_materials.append(material)
    
    return video_materials

# 更新下载视频函数，添加Wan2.1支持
async def download_videos(
    task_id: str,
    search_terms: list,
    source: str = "pexels",
    video_aspect: VideoAspect = VideoAspect.portrait,
    video_contact_mode: VideoConcatMode = VideoConcatMode.random,
    audio_duration: int = 60,
    max_clip_duration: int = 5,
    segments: List[StoryboardSegment] = None,
) -> List[str]:
    """
    下载视频素材
    
    Args:
        task_id: 任务ID
        search_terms: 搜索词列表
        source: 视频来源，可选 "pexels", "pixabay", "wan21", "wan21_custom"
        video_aspect: 视频比例
        video_contact_mode: 视频拼接模式
        audio_duration: 音频时长
        max_clip_duration: 最大片段时长
        segments: 分镜段落列表，如果提供则按分镜生成
        
    Returns:
        List[str]: 下载的视频路径列表
    """
    # 如果提供了segments参数，检查是否有视频路径
    if segments:
        logger.info(f"检测到{len(segments)}个分镜，检查是否已有视频")
        
        # 检查是否所有分镜都已有视频路径
        all_segments_have_videos = True
        segment_videos = []
        uploaded_videos = []
        
        for segment in segments:
            if segment.video_path and os.path.exists(segment.video_path):
                segment_videos.append(segment.video_path)
                # 如果是上传的视频，也添加到uploaded_videos列表
                if "uploaded" in segment.video_path:
                    uploaded_videos.append(segment.video_path)
            else:
                all_segments_have_videos = False
        
        if all_segments_have_videos:
            logger.info("所有分镜都已有视频，跳过视频生成/下载")
            return segment_videos
        
        if uploaded_videos:
            logger.info(f"发现{len(uploaded_videos)}个已上传的视频")
    
    # 检查是否使用Wan2.1生成视频
    generation_mode = config.app.get("video_generation_mode", VideoGenerationMode.pixels.value)
    if generation_mode in [VideoGenerationMode.wan21.value, VideoGenerationMode.wan21_custom.value]:
        try:
            materials = await generate_wan21_videos(search_terms, video_aspect, segments)
            generated_videos = [material.url for material in materials if material.url]
            
            # 如果生成了视频，返回
            if generated_videos:
                return generated_videos
                
            # 如果没有生成视频但有上传的视频，返回上传的视频
            if segments and 'uploaded_videos' in locals() and uploaded_videos:
                logger.info(f"没有成功生成视频，但有{len(uploaded_videos)}个已上传的视频，使用这些视频")
                return uploaded_videos
                
            # 如果既没有生成视频也没有上传的视频，返回空列表
            return []
        except Exception as e:
            logger.error(f"生成视频失败: {str(e)}")
            # 如果生成失败但有已上传的视频，返回已上传的视频
            if segments and 'uploaded_videos' in locals() and uploaded_videos:
                logger.info(f"API连接失败，但有{len(uploaded_videos)}个已上传的视频，使用这些视频")
                return uploaded_videos
            return []
    
    # 原有的视频下载逻辑
    valid_video_items = []
    valid_video_urls = []
    found_duration = 0.0
    search_videos = search_videos_pexels
    if source == "pixabay":
        search_videos = search_videos_pixabay

    for search_term in search_terms:
        video_items = search_videos(
            search_term=search_term,
            minimum_duration=max_clip_duration,
            video_aspect=video_aspect,
        )
        logger.info(f"found {len(video_items)} videos for '{search_term}'")

        for item in video_items:
            if item.url not in valid_video_urls:
                valid_video_items.append(item)
                valid_video_urls.append(item.url)
                found_duration += item.duration

    logger.info(
        f"found total videos: {len(valid_video_items)}, required duration: {audio_duration} seconds, found duration: {found_duration} seconds"
    )
    video_paths = []

    material_directory = config.app.get("material_directory", "").strip()
    if material_directory == "task":
        material_directory = utils.task_dir(task_id)
    elif material_directory and not os.path.isdir(material_directory):
        material_directory = ""

    if video_contact_mode.value == VideoConcatMode.random.value:
        random.shuffle(valid_video_items)

    total_duration = 0.0
    for item in valid_video_items:
        try:
            logger.info(f"downloading video: {item.url}")
            saved_video_path = save_video(
                video_url=item.url, save_dir=material_directory
            )
            if saved_video_path:
                logger.info(f"video saved: {saved_video_path}")
                video_paths.append(saved_video_path)
                seconds = min(max_clip_duration, item.duration)
                total_duration += seconds
                if total_duration > audio_duration:
                    logger.info(
                        f"total duration of downloaded videos: {total_duration} seconds, skip downloading more"
                    )
                    break
        except Exception as e:
            logger.error(f"failed to download video: {utils.to_json(item)} => {str(e)}")
    logger.success(f"downloaded {len(video_paths)} videos")
    return video_paths


if __name__ == "__main__":
    download_videos(
        "test123", ["Money Exchange Medium"], audio_duration=100, source="pixabay"
    )
