from queue import Queue
from typing import Dict, Any
import asyncio

from app.controllers.manager.base_manager import TaskManager


class InMemoryTaskManager(TaskManager):
    def __init__(self, max_concurrent_tasks: int):
        super().__init__(max_concurrent_tasks)
        self.tasks = {}  # 用于存储异步任务

    def create_queue(self):
        return Queue()

    def enqueue(self, task: Dict):
        self.queue.put(task)

    def dequeue(self):
        return self.queue.get()

    def is_queue_empty(self):
        return self.queue.empty()
