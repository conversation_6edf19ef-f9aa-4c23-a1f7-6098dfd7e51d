#!/usr/bin/env python3
"""
Docker环境中的MoviePy修复脚本
"""

import subprocess
import sys
import os

def run_command(cmd):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_moviepy():
    """检查MoviePy是否正确安装"""
    try:
        import moviepy
        print(f"✓ MoviePy已安装，版本: {moviepy.__version__}")
        return True
    except ImportError as e:
        print(f"✗ MoviePy导入失败: {e}")
        return False

def install_moviepy():
    """安装MoviePy"""
    print("正在安装MoviePy...")
    
    # 尝试安装MoviePy 1.0.3
    success, stdout, stderr = run_command("pip install moviepy==1.0.3")
    if success:
        print("✓ MoviePy安装成功")
        return True
    else:
        print(f"✗ MoviePy安装失败: {stderr}")
        
        # 尝试安装最新版本
        print("尝试安装最新版本...")
        success, stdout, stderr = run_command("pip install moviepy")
        if success:
            print("✓ MoviePy最新版本安装成功")
            return True
        else:
            print(f"✗ MoviePy安装失败: {stderr}")
            return False

def test_imports():
    """测试关键导入"""
    test_cases = [
        "from moviepy.editor import VideoFileClip",
        "from moviepy.editor import AudioFileClip", 
        "from moviepy.editor import ColorClip",
        "from moviepy.editor import TextClip",
        "from moviepy.editor import concatenate_videoclips",
        "from moviepy.audio.fx import volumex",
    ]
    
    failed_imports = []
    for test_case in test_cases:
        try:
            exec(test_case)
            print(f"✓ {test_case}")
        except Exception as e:
            print(f"✗ {test_case} - {e}")
            failed_imports.append(test_case)
    
    return len(failed_imports) == 0

def main():
    """主函数"""
    print("=" * 60)
    print("Docker环境MoviePy修复脚本")
    print("=" * 60)
    
    # 检查当前MoviePy状态
    if check_moviepy():
        print("MoviePy已安装，测试导入...")
        if test_imports():
            print("🎉 所有导入测试通过！")
            return True
        else:
            print("❌ 部分导入失败，尝试重新安装...")
    
    # 尝试安装MoviePy
    if install_moviepy():
        print("重新测试导入...")
        if test_imports():
            print("🎉 修复成功！所有导入测试通过！")
            return True
        else:
            print("❌ 修复失败，部分导入仍然失败")
            return False
    else:
        print("❌ 无法安装MoviePy")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
